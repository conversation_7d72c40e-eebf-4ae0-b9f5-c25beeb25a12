package router

import (
	"net/http"

	"github.com/codeandlearn1991/newsapi/internal/handler"
)

func New(ns handler.NewsStorer) *http.ServeMux {
	r := http.NewServeMux()

	r.<PERSON>("POST /news", handler.PostNews(ns))
	r.<PERSON>("GET /news", handler.GetAllNews(ns))
	r.<PERSON>("GET /news/{id}", handler.GetNewsByID(ns))
	r.<PERSON>("PUT /news/{id}", handler.UpdateNewsById(ns))
	r.<PERSON>("DELETE /news/{id}", handler.DeleteNewsByID(ns))

	return r
}